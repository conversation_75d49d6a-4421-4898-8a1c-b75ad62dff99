import { query } from '../lib/database';
import { Book } from '../types';

export class BookService {
  // Get all books
  static async getAllBooks(): Promise<Book[]> {
    try {
      const sql = `
        SELECT id, title, author, isbn, category, publisher, publish_year, 
               stock, rent_price, description, image_url, created_at, updated_at
        FROM books 
        ORDER BY created_at DESC
      `;
      
      const books = await query(sql);
      
      return books.map((book: any) => ({
        id: book.id,
        title: book.title,
        author: book.author,
        isbn: book.isbn,
        category: book.category,
        publisher: book.publisher,
        publishYear: book.publish_year,
        stock: book.stock,
        rentPrice: parseFloat(book.rent_price),
        description: book.description,
        imageUrl: book.image_url,
        createdAt: book.created_at,
        updatedAt: book.updated_at
      }));
    } catch (error) {
      console.error('Error getting all books:', error);
      return [];
    }
  }
  
  // Get book by ID
  static async getBookById(id: string): Promise<Book | null> {
    try {
      const sql = `
        SELECT id, title, author, isbn, category, publisher, publish_year, 
               stock, rent_price, description, image_url, created_at, updated_at
        FROM books 
        WHERE id = ?
      `;
      
      const books = await query(sql, [id]);
      
      if (books.length === 0) {
        return null;
      }
      
      const book = books[0];
      return {
        id: book.id,
        title: book.title,
        author: book.author,
        isbn: book.isbn,
        category: book.category,
        publisher: book.publisher,
        publishYear: book.publish_year,
        stock: book.stock,
        rentPrice: parseFloat(book.rent_price),
        description: book.description,
        imageUrl: book.image_url,
        createdAt: book.created_at,
        updatedAt: book.updated_at
      };
    } catch (error) {
      console.error('Error getting book by ID:', error);
      return null;
    }
  }
  
  // Create new book
  static async createBook(bookData: Omit<Book, 'id' | 'createdAt' | 'updatedAt'>): Promise<Book | null> {
    try {
      const { title, author, isbn, category, publisher, publishYear, stock, rentPrice, description, imageUrl } = bookData;
      
      const sql = `
        INSERT INTO books (title, author, isbn, category, publisher, publish_year, stock, rent_price, description, image_url) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const result = await query(sql, [title, author, isbn, category, publisher, publishYear, stock, rentPrice, description, imageUrl]);
      
      if (result.insertId) {
        return await this.getBookById(result.insertId);
      }
      
      return null;
    } catch (error) {
      console.error('Error creating book:', error);
      return null;
    }
  }
  
  // Update book
  static async updateBook(id: string, bookData: Partial<Book>): Promise<Book | null> {
    try {
      const { title, author, isbn, category, publisher, publishYear, stock, rentPrice, description, imageUrl } = bookData;
      
      const sql = `
        UPDATE books 
        SET title = ?, author = ?, isbn = ?, category = ?, publisher = ?, 
            publish_year = ?, stock = ?, rent_price = ?, description = ?, 
            image_url = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `;
      
      await query(sql, [title, author, isbn, category, publisher, publishYear, stock, rentPrice, description, imageUrl, id]);
      
      return await this.getBookById(id);
    } catch (error) {
      console.error('Error updating book:', error);
      return null;
    }
  }
  
  // Delete book
  static async deleteBook(id: string): Promise<boolean> {
    try {
      const sql = 'DELETE FROM books WHERE id = ?';
      await query(sql, [id]);
      return true;
    } catch (error) {
      console.error('Error deleting book:', error);
      return false;
    }
  }
  
  // Search books
  static async searchBooks(searchTerm: string): Promise<Book[]> {
    try {
      const sql = `
        SELECT id, title, author, isbn, category, publisher, publish_year, 
               stock, rent_price, description, image_url, created_at, updated_at
        FROM books 
        WHERE title LIKE ? OR author LIKE ? OR category LIKE ? OR isbn LIKE ?
        ORDER BY title
      `;
      
      const searchPattern = `%${searchTerm}%`;
      const books = await query(sql, [searchPattern, searchPattern, searchPattern, searchPattern]);
      
      return books.map((book: any) => ({
        id: book.id,
        title: book.title,
        author: book.author,
        isbn: book.isbn,
        category: book.category,
        publisher: book.publisher,
        publishYear: book.publish_year,
        stock: book.stock,
        rentPrice: parseFloat(book.rent_price),
        description: book.description,
        imageUrl: book.image_url,
        createdAt: book.created_at,
        updatedAt: book.updated_at
      }));
    } catch (error) {
      console.error('Error searching books:', error);
      return [];
    }
  }
  
  // Get books by category
  static async getBooksByCategory(category: string): Promise<Book[]> {
    try {
      const sql = `
        SELECT id, title, author, isbn, category, publisher, publish_year, 
               stock, rent_price, description, image_url, created_at, updated_at
        FROM books 
        WHERE category = ?
        ORDER BY title
      `;
      
      const books = await query(sql, [category]);
      
      return books.map((book: any) => ({
        id: book.id,
        title: book.title,
        author: book.author,
        isbn: book.isbn,
        category: book.category,
        publisher: book.publisher,
        publishYear: book.publish_year,
        stock: book.stock,
        rentPrice: parseFloat(book.rent_price),
        description: book.description,
        imageUrl: book.image_url,
        createdAt: book.created_at,
        updatedAt: book.updated_at
      }));
    } catch (error) {
      console.error('Error getting books by category:', error);
      return [];
    }
  }
  
  // Update book stock
  static async updateStock(id: string, newStock: number): Promise<boolean> {
    try {
      const sql = 'UPDATE books SET stock = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
      await query(sql, [newStock, id]);
      return true;
    } catch (error) {
      console.error('Error updating book stock:', error);
      return false;
    }
  }
}
