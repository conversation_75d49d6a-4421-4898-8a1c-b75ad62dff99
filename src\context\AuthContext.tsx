import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, LoginCredentials, AuthContextType } from '../types';
import { mockUsers, mockCredentials } from '../data/mockData';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check for existing session on mount
  useEffect(() => {
    const checkExistingSession = () => {
      try {
        const savedUser = localStorage.getItem('lsp_jwp_user');
        const sessionExpiry = localStorage.getItem('lsp_jwp_session_expiry');
        
        if (savedUser && sessionExpiry) {
          const expiryTime = new Date(sessionExpiry);
          const currentTime = new Date();
          
          if (currentTime < expiryTime) {
            setUser(JSON.parse(savedUser));
          } else {
            // Session expired, clear storage
            localStorage.removeItem('lsp_jwp_user');
            localStorage.removeItem('lsp_jwp_session_expiry');
          }
        }
      } catch (error) {
        console.error('Error checking existing session:', error);
        localStorage.removeItem('lsp_jwp_user');
        localStorage.removeItem('lsp_jwp_session_expiry');
      } finally {
        setIsLoading(false);
      }
    };

    checkExistingSession();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check credentials against mock data
      const isValidCredentials = Object.values(mockCredentials).some(
        cred => cred.username === credentials.username && cred.password === credentials.password
      );
      
      if (isValidCredentials) {
        const foundUser = mockUsers.find(u => u.username === credentials.username);
        
        if (foundUser && foundUser.isActive) {
          // Update last login time
          const updatedUser = {
            ...foundUser,
            lastLogin: new Date().toISOString()
          };
          
          setUser(updatedUser);
          
          // Save to localStorage with 24 hour expiry
          const expiryTime = new Date();
          expiryTime.setHours(expiryTime.getHours() + 24);
          
          localStorage.setItem('lsp_jwp_user', JSON.stringify(updatedUser));
          localStorage.setItem('lsp_jwp_session_expiry', expiryTime.toISOString());
          
          return true;
        }
      }
      
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('lsp_jwp_user');
    localStorage.removeItem('lsp_jwp_session_expiry');
  };

  const value: AuthContextType = {
    user,
    login,
    logout,
    isLoading,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
