import mysql from 'mysql2/promise';

// Database configuration
const dbConfig = {
  host: import.meta.env.VITE_DB_HOST || '127.0.0.1',
  user: import.meta.env.VITE_DB_USER || 'root',
  password: import.meta.env.VITE_DB_PASSWORD || '',
  database: import.meta.env.VITE_DB_NAME || 'lsp_jwp',
  port: parseInt(import.meta.env.VITE_DB_PORT || '3306'),
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// Create connection pool
let pool: mysql.Pool | null = null;

export const createConnection = async (): Promise<mysql.Pool> => {
  if (!pool) {
    try {
      pool = mysql.createPool(dbConfig);
      console.log('Database connection pool created successfully');
      
      // Test the connection
      const connection = await pool.getConnection();
      await connection.ping();
      connection.release();
      console.log('Database connection test successful');
      
    } catch (error) {
      console.error('Error creating database connection:', error);
      throw error;
    }
  }
  return pool;
};

export const getConnection = async (): Promise<mysql.Pool> => {
  if (!pool) {
    return await createConnection();
  }
  return pool;
};

export const closeConnection = async (): Promise<void> => {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('Database connection pool closed');
  }
};

// Database query helper
export const query = async (sql: string, params?: any[]): Promise<any> => {
  try {
    const connection = await getConnection();
    const [results] = await connection.execute(sql, params);
    return results;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
};

// Database transaction helper
export const transaction = async (callback: (connection: mysql.PoolConnection) => Promise<any>): Promise<any> => {
  const pool = await getConnection();
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
};

export default { createConnection, getConnection, closeConnection, query, transaction };
