import { query } from '../lib/database';
import { User, LoginCredentials } from '../types';

export class UserService {
  // Authenticate user with username and password
  static async authenticate(credentials: LoginCredentials): Promise<User | null> {
    try {
      const { username, password } = credentials;
      
      // For now, we'll use simple password comparison
      // In production, you should use proper password hashing (bcrypt)
      const sql = `
        SELECT id, username, email, name, role, is_active, created_at, last_login 
        FROM users 
        WHERE username = ? AND is_active = TRUE
      `;
      
      const users = await query(sql, [username]);
      
      if (users.length === 0) {
        return null;
      }
      
      const user = users[0];
      
      // Simple password check (replace with bcrypt in production)
      const validPassword = this.checkPassword(password, username);
      
      if (!validPassword) {
        return null;
      }
      
      // Update last login
      await this.updateLastLogin(user.id);
      
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.is_active,
        createdAt: user.created_at,
        lastLogin: new Date().toISOString()
      };
    } catch (error) {
      console.error('Authentication error:', error);
      return null;
    }
  }
  
  // Simple password check (replace with bcrypt in production)
  private static checkPassword(password: string, username: string): boolean {
    // For demo purposes, using simple password mapping
    const passwordMap: { [key: string]: string } = {
      'admin': 'admin123',
      'staff1': 'staff123'
    };
    
    return passwordMap[username] === password;
  }
  
  // Update user's last login timestamp
  static async updateLastLogin(userId: string): Promise<void> {
    try {
      const sql = 'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?';
      await query(sql, [userId]);
    } catch (error) {
      console.error('Error updating last login:', error);
    }
  }
  
  // Get user by ID
  static async getUserById(id: string): Promise<User | null> {
    try {
      const sql = `
        SELECT id, username, email, name, role, is_active, created_at, last_login 
        FROM users 
        WHERE id = ? AND is_active = TRUE
      `;
      
      const users = await query(sql, [id]);
      
      if (users.length === 0) {
        return null;
      }
      
      const user = users[0];
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.is_active,
        createdAt: user.created_at,
        lastLogin: user.last_login
      };
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }
  
  // Get all users
  static async getAllUsers(): Promise<User[]> {
    try {
      const sql = `
        SELECT id, username, email, name, role, is_active, created_at, last_login 
        FROM users 
        ORDER BY created_at DESC
      `;
      
      const users = await query(sql);
      
      return users.map((user: any) => ({
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.is_active,
        createdAt: user.created_at,
        lastLogin: user.last_login
      }));
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  }
  
  // Create new user
  static async createUser(userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'> & { password: string }): Promise<User | null> {
    try {
      const { username, email, name, role, isActive, password } = userData;
      
      const sql = `
        INSERT INTO users (username, email, password_hash, name, role, is_active) 
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      
      // In production, hash the password with bcrypt
      const passwordHash = password; // Replace with bcrypt.hash(password, 10)
      
      const result = await query(sql, [username, email, passwordHash, name, role, isActive]);
      
      if (result.insertId) {
        return await this.getUserById(result.insertId);
      }
      
      return null;
    } catch (error) {
      console.error('Error creating user:', error);
      return null;
    }
  }
  
  // Update user
  static async updateUser(id: string, userData: Partial<User>): Promise<User | null> {
    try {
      const { username, email, name, role, isActive } = userData;
      
      const sql = `
        UPDATE users 
        SET username = ?, email = ?, name = ?, role = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `;
      
      await query(sql, [username, email, name, role, isActive, id]);
      
      return await this.getUserById(id);
    } catch (error) {
      console.error('Error updating user:', error);
      return null;
    }
  }
  
  // Delete user (soft delete by setting is_active to false)
  static async deleteUser(id: string): Promise<boolean> {
    try {
      const sql = 'UPDATE users SET is_active = FALSE WHERE id = ?';
      await query(sql, [id]);
      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }
}
