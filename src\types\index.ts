
export interface Book {
  id: string;
  title: string;
  author: string;
  isbn: string;
  category: string;
  publisher: string;
  publishYear: number;
  stock: number;
  rentPrice: number;
  description: string;
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  membershipType: 'bronze' | 'silver' | 'gold';
  joinDate: string;
  totalRentals: number;
  status: 'active' | 'inactive';
}

export interface Rental {
  id: string;
  bookId: string;
  customerId: string;
  startDate: string;
  endDate: string;
  returnDate?: string;
  status: 'active' | 'returned' | 'overdue';
  totalPrice: number;
  lateFee?: number;
  notes?: string;
}

export interface DashboardStats {
  totalBooks: number;
  totalCustomers: number;
  activeRentals: number;
  totalRevenue: number;
  overdueRentals: number;
  availableBooks: number;
}

export interface User {
  id: string;
  username: string;
  email: string;
  name: string;
  role: 'admin' | 'staff';
  isActive: boolean;
  createdAt: string;
  lastLogin?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthContextType {
  user: User | null;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}
