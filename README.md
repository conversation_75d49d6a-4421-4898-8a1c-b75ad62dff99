# LSP JWP - Sistem Rental Buku

Sistem manajemen rental buku untuk LSP JWP yang dibangun dengan teknologi modern.

## Fitur Utama

- **Sistem Login Admin** - Autentikasi untuk admin dan staff
- **Dashboard Admin** - Overview statistik dan aktivitas rental
- **Manajemen Buku** - CRUD buku dengan kategori dan stok
- **Manajemen Customer** - Kelola data pelanggan dan membership
- **Manajemen Rental** - Proses rental, pengembalian, dan tracking
- **Protected Routes** - Keamanan akses halaman berdasarkan role

## Teknologi yang Digunakan

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **UI Framework**: Tailwind CSS + shadcn/ui
- **State Management**: React Hooks
- **Routing**: React Router DOM
- **Database**: MySQL (akan diintegrasikan)

## Instalasi dan Penggunaan

### Prasyarat
- Node.js (versi 18 atau lebih baru)
- npm atau yarn

### Langkah Instalasi

```bash
# 1. Clone repository
git clone <repository-url>

# 2. Masuk ke direktori project
cd LSP-JWP

# 3. Install dependencies
npm install

# 4. Jalankan development server
npm run dev
```

### Perintah yang Tersedia

```bash
# Development server
npm run dev

# Build untuk production
npm run build

# Build untuk development
npm run build:dev

# Lint code
npm run lint

# Preview build
npm run preview
```

## Login Credentials

Untuk mengakses sistem, gunakan kredensial berikut:

### Admin
- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Administrator (akses penuh)

### Staff
- **Username**: `staff1`
- **Password**: `staff123`
- **Role**: Staff (akses terbatas)

### Fitur Autentikasi
- Session management dengan localStorage
- Auto-logout setelah 24 jam
- Protected routes berdasarkan role
- Responsive login form
- Demo credentials untuk testing

## Struktur Project

```
src/
├── components/          # Komponen React
│   ├── ui/             # Komponen UI dasar (shadcn/ui)
│   ├── Dashboard.tsx   # Komponen dashboard
│   ├── BookManager.tsx # Manajemen buku
│   ├── CustomerManager.tsx # Manajemen customer
│   ├── RentalManager.tsx   # Manajemen rental
│   ├── Header.tsx      # Header dengan user menu
│   ├── Sidebar.tsx     # Sidebar navigasi
│   └── ProtectedRoute.tsx # Komponen proteksi route
├── context/            # React Context
│   └── AuthContext.tsx # Context autentikasi
├── data/               # Data mock dan utilities
├── pages/              # Halaman utama
│   ├── Index.tsx       # Dashboard utama
│   ├── Login.tsx       # Halaman login
│   └── NotFound.tsx    # Halaman 404
├── types/              # TypeScript type definitions
└── lib/                # Utilities dan helpers
```

## Database

Project ini akan menggunakan MySQL database dengan konfigurasi:
- Host: 127.0.0.1
- User: root
- Database: lsp_jwp
- Port: 3306

## Kontribusi

1. Fork repository
2. Buat branch fitur (`git checkout -b feature/nama-fitur`)
3. Commit perubahan (`git commit -m 'Menambah fitur baru'`)
4. Push ke branch (`git push origin feature/nama-fitur`)
5. Buat Pull Request
