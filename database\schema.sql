-- LSP JWP Database Schema
-- Create database if not exists
CREATE DATABASE IF NOT EXISTS lsp_jwp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE lsp_jwp;

-- Users table for authentication
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'staff') NOT NULL DEFAULT 'staff',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Books table
CREATE TABLE IF NOT EXISTS books (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    title VARCHAR(255) NOT NULL,
    author <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    isbn VARCHAR(20) UNIQUE,
    category VARCHAR(100) NOT NULL,
    publisher VARCHAR(255),
    publish_year INT,
    stock INT DEFAULT 0,
    rent_price DECIMAL(10,2) NOT NULL,
    description TEXT,
    image_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_title (title),
    INDEX idx_author (author),
    INDEX idx_category (category),
    INDEX idx_isbn (isbn)
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    membership_type ENUM('bronze', 'silver', 'gold') DEFAULT 'bronze',
    join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_rentals INT DEFAULT 0,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_membership (membership_type)
);

-- Rentals table
CREATE TABLE IF NOT EXISTS rentals (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    book_id VARCHAR(36) NOT NULL,
    customer_id VARCHAR(36) NOT NULL,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    return_date TIMESTAMP NULL,
    status ENUM('active', 'returned', 'overdue') DEFAULT 'active',
    total_price DECIMAL(10,2) NOT NULL,
    late_fee DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_book_id (book_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date)
);

-- Insert default admin user (password: admin123)
INSERT INTO users (id, username, email, password_hash, name, role, is_active) VALUES 
('1', 'admin', '<EMAIL>', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'Administrator LSP JWP', 'admin', TRUE),
('2', 'staff1', '<EMAIL>', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'Staff Perpustakaan', 'staff', TRUE)
ON DUPLICATE KEY UPDATE username = username;

-- Insert sample books
INSERT INTO books (id, title, author, isbn, category, publisher, publish_year, stock, rent_price, description, image_url) VALUES 
('1', 'The Great Gatsby', 'F. Scott Fitzgerald', '978-0-7432-7356-5', 'Fiction', 'Scribner', 1925, 5, 15000, 'A classic American novel set in the Jazz Age', 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=300&h=400&fit=crop'),
('2', 'To Kill a Mockingbird', 'Harper Lee', '978-0-06-112008-4', 'Fiction', 'J.B. Lippincott & Co.', 1960, 3, 12000, 'A gripping tale of racial injustice and childhood innocence', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=400&fit=crop'),
('3', '1984', 'George Orwell', '978-0-452-28423-4', 'Dystopian Fiction', 'Secker & Warburg', 1949, 7, 18000, 'A dystopian social science fiction novel', 'https://images.unsplash.com/photo-1495640388908-05fa85288e61?w=300&h=400&fit=crop')
ON DUPLICATE KEY UPDATE title = title;

-- Insert sample customers
INSERT INTO customers (id, name, email, phone, address, membership_type, total_rentals, status) VALUES 
('1', 'Ahmad Rizki', '<EMAIL>', '081234567890', 'Jl. Sudirman No. 123, Jakarta', 'gold', 25, 'active'),
('2', 'Siti Nurhaliza', '<EMAIL>', '082345678901', 'Jl. Thamrin No. 456, Jakarta', 'silver', 12, 'active'),
('3', 'Budi Santoso', '<EMAIL>', '083456789012', 'Jl. Gatot Subroto No. 789, Jakarta', 'bronze', 4, 'active')
ON DUPLICATE KEY UPDATE name = name;
